/**
 * Enhanced Sync Service for Canva Editor
 * Provides unified synchronization with conflict resolution and proper ID management
 */

import { designService } from './designService';
import { generateObjectId, requestDeduplicator } from '../utils/idGenerator';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import { domToPng } from 'modern-screenshot';

export interface SyncState {
  designId: string | null;
  designName: string;
  isDirty: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
  lastError: string | null;
  syncStatus: 'idle' | 'saving' | 'saved' | 'error' | 'offline';
  isOnline: boolean;
  retryCount: number;
  conflictResolution: 'none' | 'local' | 'remote';
}

export interface SyncOptions {
  autoSaveInterval?: number;
  debounceDelay?: number;
  maxRetries?: number;
  enableLocalBackup?: boolean;
  showNotifications?: boolean;
  enableOptimisticUpdates?: boolean;
  enableConflictResolution?: boolean;
  previewImageQuality?: number;
  previewImageSize?: { width: number; height: number };
}

export interface DesignData {
  [key: string]: any;
}

export interface PreviewImageOptions {
  element: HTMLElement;
  width?: number;
  height?: number;
  quality?: number;
}

export interface SyncQueueItem {
  id: string;
  designId: string;
  data: DesignData;
  designName: string;
  previewImage?: string;
  timestamp: number;
  retryCount: number;
}

/**
 * Enhanced Sync Service Class
 */
export class EnhancedSyncService {
  private state: SyncState = {
    designId: null,
    designName: 'Untitled Design',
    isDirty: false,
    isSaving: false,
    lastSaved: null,
    lastError: null,
    syncStatus: 'idle',
    isOnline: navigator.onLine,
    retryCount: 0,
    conflictResolution: 'none',
  };

  private options: Required<SyncOptions> = {
    autoSaveInterval: 30000, // 30 seconds
    debounceDelay: 3000, // 3 seconds
    maxRetries: 3,
    enableLocalBackup: true,
    showNotifications: true,
    enableOptimisticUpdates: true,
    enableConflictResolution: true,
    previewImageQuality: 0.8,
    previewImageSize: { width: 300, height: 300 },
  };

  private listeners: Array<(state: SyncState) => void> = [];
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;
  private previewDebounceTimer: NodeJS.Timeout | null = null;
  private lastDataSnapshot: string = '';
  private lastPreviewSnapshot: string = '';
  private userId: string | null = null;
  private syncQueue: SyncQueueItem[] = [];
  private isProcessingQueue: boolean = false;
  private onlineStatusListener: (() => void) | null = null;
  private API_BASE_URL: string = 'http://localhost:4000/api';

  constructor(options: Partial<SyncOptions> = {}) {
    this.options = { ...this.options, ...options };
    this.setupOnlineStatusListener();
  }

  /**
   * Initialize the sync service with user context
   * Note: This does NOT load design data - that should be done separately via loadDesign()
   */
  initialize(userId: string, designId?: string, designName?: string): void {
    console.log('🔄 Initializing Enhanced Sync Service', { userId, designId, designName });

    this.userId = userId;
    this.updateState({
      designId: designId || null,
      designName: designName || 'Untitled Design',
      isDirty: false,
      isSaving: false,
      lastSaved: null,
      lastError: null,
      syncStatus: 'idle',
    });

    // Only start auto-save if we have a design ID
    if (designId) {
      this.startAutoSave();
    }

    console.log('✅ Enhanced Sync Service initialized (data loading handled separately)');
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: SyncState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current state
   */
  getState(): SyncState {
    return { ...this.state };
  }

  /**
   * Update design data and trigger sync
   */
  updateDesignData(
    data: DesignData,
    designName?: string,
    pageElement?: HTMLElement
  ): void {
    const dataSnapshot = JSON.stringify(data);

    // Check if data actually changed
    if (dataSnapshot === this.lastDataSnapshot) {
      return;
    }

    this.lastDataSnapshot = dataSnapshot;

    // Update state
    this.updateState({
      isDirty: true,
      designName: designName || this.state.designName,
    });

    // Save to local backup if enabled
    if (this.options.enableLocalBackup) {
      this.saveToLocalBackup(data, this.state.designName);
    }

    // Generate preview image if element is provided
    let previewPromise: Promise<string> = Promise.resolve('');
    if (pageElement) {
      console.log('📸 Page element provided for preview generation:', {
        tagName: pageElement.tagName,
        className: pageElement.className,
        width: pageElement.clientWidth,
        height: pageElement.clientHeight
      });
      previewPromise = this.debouncedGeneratePreview({
        element: pageElement,
      });
    } else {
      console.log('⚠️ No page element provided for preview generation');
    }

    // Trigger debounced save with preview
    previewPromise.then(previewImage => {
      this.debouncedSave(data, previewImage);
    });
  }

  /**
   * Force immediate save
   */
  async saveNow(
    data: DesignData,
    pageElement?: HTMLElement
  ): Promise<boolean> {
    // Generate preview image if element is provided
    let previewImage = '';
    if (pageElement) {
      try {
        previewImage = await this.generatePreviewImage({
          element: pageElement,
        });
      } catch (error) {
        console.warn('⚠️ Failed to generate preview image for manual save:', error);
      }
    }

    return this.performSave(data, true, previewImage);
  }

  /**
   * Load existing design data from server
   */
  async loadDesign(designId: string): Promise<{ success: boolean; data?: DesignData; designName?: string; error?: string }> {
    try {
      console.log(`📥 Loading design data for ID: ${designId}`);

      this.updateState({
        syncStatus: 'saving', // Use saving status to show loading
        lastError: null,
      });

      // Validate design ID format
      const isValidObjectId = (id: string) => /^[0-9a-fA-F]{24}$/.test(id);
      if (!isValidObjectId(designId)) {
        throw new Error(`Invalid design ID format: ${designId}`);
      }

      // Fetch design metadata
      const response = await axios.get(`${this.API_BASE_URL}/templates/${designId}`, {
        timeout: 30000,
        headers: { 'Accept': 'application/json' },
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to fetch design');
      }

      const design = response.data.template || response.data;

      if (!design) {
        throw new Error('Design not found');
      }

      console.log(`✅ Design metadata loaded: ${design.title}`);

      // Update sync service state with loaded design
      this.updateState({
        designId,
        designName: design.title || 'Untitled Design',
        syncStatus: 'saved',
        lastSaved: design.updatedAt ? new Date(design.updatedAt) : null,
        isDirty: false,
        lastError: null,
      });

      // Load design data if available
      if (design.templateUrl) {
        try {
          console.log(`📥 Loading design data from URL: ${design.templateUrl}`);

          // Use the proxy-template endpoint with proper URL encoding
          const proxyUrl = `${this.API_BASE_URL}/proxy-template/${encodeURIComponent(design.templateUrl)}`;
          console.log(`📥 Proxy URL: ${proxyUrl}`);

          const templateResponse = await axios.get(proxyUrl, {
            timeout: 30000,
            headers: { 'Accept': 'application/json' },
          });

          if (templateResponse.data) {
            console.log(`✅ Design data loaded successfully`);
            return {
              success: true,
              data: templateResponse.data,
              designName: design.title || 'Untitled Design',
            };
          } else {
            throw new Error('Empty design data received');
          }
        } catch (templateError) {
          console.warn('⚠️ Failed to load design data from URL:', templateError);
          throw new Error('Failed to load design content');
        }
      } else if (design.packedData && design.packedData.length > 0) {
        // Use packed data if available
        console.log(`✅ Using packed data from design`);
        return {
          success: true,
          data: design.packedData[0], // Use first packed data item
          designName: design.title || 'Untitled Design',
        };
      } else {
        // No design data available - return empty template
        console.log(`⚠️ No design data found, returning empty template`);
        return {
          success: true,
          data: { pages: [] }, // Empty template
          designName: design.title || 'Untitled Design',
        };
      }

    } catch (error) {
      console.error('❌ Failed to load design:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      this.updateState({
        syncStatus: 'error',
        lastError: errorMessage,
        isSaving: false,
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Create a new design with pre-generated ID
   */
  async createNewDesign(
    data: DesignData,
    designName: string,
    options: {
      width?: number;
      height?: number;
      backgroundColor?: string;
      isCoupon?: boolean;
    } = {}
  ): Promise<string | null> {
    if (!this.userId) {
      throw new Error('User ID not set. Call initialize() first.');
    }

    try {
      this.updateState({ isSaving: true, syncStatus: 'saving' });

      const response = await designService.createDesignWithRetry({
        templateName: designName,
        templateDesc: `New design created on ${new Date().toLocaleDateString()}`,
        userId: this.userId,
        packedData: [data], // Wrap in array as expected by API
        previewImage: '', // Will be generated later
        tags: options.isCoupon ? ['coupon'] : [],
        isPublic: false,
        isKiosk: false,
        isCoupon: options.isCoupon || false,
        width: options.width,
        height: options.height,
        backgroundColor: options.backgroundColor,
      });

      const designId = response.template.id;
      
      this.updateState({
        designId,
        designName,
        isDirty: false,
        isSaving: false,
        lastSaved: new Date(),
        lastError: null,
        syncStatus: 'success',
      });

      console.log(`✅ New design created with ID: ${designId}`);
      return designId;

    } catch (error: any) {
      console.error('❌ Failed to create new design:', error);
      
      this.updateState({
        isSaving: false,
        lastError: error.message,
        syncStatus: 'error',
      });

      throw error;
    }
  }

  /**
   * Update design name
   */
  updateDesignName(newName: string): void {
    if (newName !== this.state.designName) {
      this.updateState({
        designName: newName,
        isDirty: true,
      });
    }
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.listeners = [];
  }

  // Private methods

  private updateState(updates: Partial<SyncState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    this.autoSaveTimer = setInterval(() => {
      if (this.state.isDirty && !this.state.isSaving) {
        console.log('⏰ Auto-save triggered by timer');
        // We need the current data, but we don't have it here
        // This will be handled by the component using this service
      }
    }, this.options.autoSaveInterval);
  }

  private debouncedSave(data: DesignData, previewImage?: string): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      if (!this.state.isSaving) {
        this.performSave(data, false, previewImage);
      }
    }, this.options.debounceDelay);
  }

  private async performSave(
    data: DesignData,
    isManual: boolean,
    previewImage?: string
  ): Promise<boolean> {
    if (!this.userId) {
      console.error('❌ Cannot save: User ID not set');
      return false;
    }

    if (this.state.isSaving) {
      console.log('⏸️ Save already in progress, skipping');
      return false;
    }

    try {
      this.updateState({ isSaving: true, syncStatus: 'saving' });

      const saveKey = `save-${this.state.designId || 'new'}-${Date.now()}`;

      // Create a queue item with preview image if available
      const queueItem: SyncQueueItem = {
        id: generateObjectId(),
        designId: this.state.designId || 'new',
        data,
        designName: this.state.designName,
        previewImage,
        timestamp: Date.now(),
        retryCount: 0,
      };

      // Use request deduplication for non-manual saves
      const saveOperation = isManual
        ? this.syncItemToServer(queueItem)
        : requestDeduplicator.deduplicate(saveKey, () => this.syncItemToServer(queueItem));

      await saveOperation;

      this.updateState({
        isDirty: false,
        isSaving: false,
        lastSaved: new Date(),
        lastError: null,
        syncStatus: 'saved',
      });

      if (isManual && this.options.showNotifications) {
        toast.success('Design saved successfully!');
      }

      return true;

    } catch (error: any) {
      console.error('❌ Save failed:', error);
      
      this.updateState({
        isSaving: false,
        lastError: error.message,
        syncStatus: 'error',
      });

      if (this.options.showNotifications) {
        toast.error('Failed to save design', {
          description: error.message,
          duration: 5000,
        });
      }

      return false;
    }
  }

  private async executeSave(data: DesignData): Promise<void> {
    // Implementation depends on whether this is a new design or existing one
    if (!this.state.designId) {
      // This is a new design - should have been created via createNewDesign
      throw new Error('Cannot save design without ID. Create design first.');
    }

    console.log(`💾 Saving design ${this.state.designId}...`);

    // If we're offline, add to sync queue
    if (!navigator.onLine) {
      this.addToSyncQueue({
        designId: this.state.designId,
        data,
        designName: this.state.designName,
      });

      this.updateState({
        syncStatus: 'offline',
        lastError: 'Device is offline. Changes will be synced when connection is restored.',
      });

      return;
    }

    // Try to sync directly to server
    try {
      await this.syncItemToServer({
        id: generateObjectId(),
        designId: this.state.designId,
        data,
        designName: this.state.designName,
        timestamp: Date.now(),
        retryCount: 0,
      });
    } catch (error) {
      // If direct sync fails, add to queue for retry
      this.addToSyncQueue({
        designId: this.state.designId,
        data,
        designName: this.state.designName,
      });

      throw error;
    }
  }

  private saveToLocalBackup(data: DesignData, designName: string): void {
    try {
      const backup = {
        designId: this.state.designId,
        designName,
        data,
        timestamp: Date.now(),
      };

      localStorage.setItem('design_backup', JSON.stringify(backup));
      console.log('💾 Design backed up to localStorage');
    } catch (error) {
      console.warn('⚠️ Failed to save local backup:', error);
    }
  }

  /**
   * Generate a high-quality preview image from the design
   */
  async generatePreviewImage(options: PreviewImageOptions): Promise<string> {
    if (!options.element) {
      console.warn('⚠️ No element provided for preview generation');
      return '';
    }

    try {
      console.log('📸 Starting preview image generation...', {
        elementTag: options.element.tagName,
        elementClass: options.element.className,
        elementWidth: options.element.clientWidth,
        elementHeight: options.element.clientHeight,
      });

      // Calculate dimensions while maintaining aspect ratio
      const elementWidth = options.element.clientWidth;
      const elementHeight = options.element.clientHeight;

      if (elementWidth === 0 || elementHeight === 0) {
        console.warn('⚠️ Element has zero dimensions, cannot generate preview');
        return '';
      }

      const aspectRatio = elementWidth / elementHeight;

      // Default to 300x300 or use provided dimensions
      const maxWidth = options.width || this.options.previewImageSize.width;
      const maxHeight = options.height || this.options.previewImageSize.height;

      let targetWidth, targetHeight;

      if (aspectRatio > 1) {
        // Landscape orientation
        targetWidth = Math.min(maxWidth, elementWidth);
        targetHeight = targetWidth / aspectRatio;
      } else {
        // Portrait or square orientation
        targetHeight = Math.min(maxHeight, elementHeight);
        targetWidth = targetHeight * aspectRatio;
      }

      console.log(`🖼️ Generating preview image at ${Math.round(targetWidth)}x${Math.round(targetHeight)}`);

      // Generate the image using domToPng
      const previewImage = await domToPng(options.element, {
        width: Math.round(targetWidth),
        height: Math.round(targetHeight),
        quality: options.quality || this.options.previewImageQuality,
      });

      console.log(`✅ Preview image generated successfully, size: ${previewImage.length} bytes`);
      return previewImage;
    } catch (error) {
      console.error('❌ Failed to generate preview image:', error);
      return '';
    }
  }

  /**
   * Debounced preview image generation
   */
  debouncedGeneratePreview(options: PreviewImageOptions): Promise<string> {
    return new Promise((resolve) => {
      // Clear any existing debounce timer
      if (this.previewDebounceTimer) {
        clearTimeout(this.previewDebounceTimer);
      }

      // Set a new debounce timer
      this.previewDebounceTimer = setTimeout(async () => {
        const previewImage = await this.generatePreviewImage(options);
        resolve(previewImage);
      }, 500); // 500ms debounce delay for preview generation
    });
  }

  /**
   * Set up online status listener
   */
  private setupOnlineStatusListener(): void {
    // Clean up any existing listener
    if (this.onlineStatusListener) {
      window.removeEventListener('online', this.onlineStatusListener);
      window.removeEventListener('offline', this.onlineStatusListener);
    }

    // Create a new listener
    this.onlineStatusListener = () => {
      const isOnline = navigator.onLine;

      // Update state with online status
      this.updateState({
        isOnline,
        syncStatus: isOnline ? this.state.syncStatus : 'offline',
      });

      // If we're back online, process the sync queue
      if (isOnline && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    };

    // Add the listener to online/offline events
    window.addEventListener('online', this.onlineStatusListener);
    window.addEventListener('offline', this.onlineStatusListener);

    // Set initial state
    this.updateState({ isOnline: navigator.onLine });
  }

  /**
   * Add an item to the sync queue
   */
  private addToSyncQueue(item: Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>): void {
    const queueItem: SyncQueueItem = {
      ...item,
      id: generateObjectId(),
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.syncQueue.push(queueItem);
    console.log(`📋 Added item to sync queue, queue length: ${this.syncQueue.length}`);

    // If we're online, process the queue
    if (navigator.onLine && !this.isProcessingQueue) {
      this.processSyncQueue();
    }
  }

  /**
   * Process the sync queue
   */
  private async processSyncQueue(): Promise<void> {
    // If we're offline or already processing, return
    if (!navigator.onLine || this.isProcessingQueue || this.syncQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`🔄 Processing sync queue, ${this.syncQueue.length} items remaining`);

    try {
      // Get the next item from the queue
      const item = this.syncQueue[0];

      // Try to sync the item
      await this.syncItemToServer(item);

      // If successful, remove the item from the queue
      this.syncQueue.shift();

      // If there are more items in the queue, process the next one
      if (this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    } catch (error) {
      console.error('❌ Failed to process sync queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Sync a queue item to the server
   */
  private async syncItemToServer(item: SyncQueueItem): Promise<void> {
    try {
      // Prepare the API request
      const apiData = {
        packedData: [item.data],
        previewImage: item.previewImage || '',
        templateName: item.designName,
        templateDesc: '',
        tags: [],
        userId: this.userId,
      };

      console.log('📤 Syncing to server:', {
        designId: item.designId,
        designName: item.designName,
        hasPreviewImage: !!(item.previewImage && item.previewImage.length > 0),
        previewImageSize: item.previewImage ? item.previewImage.length : 0,
      });

      // Make the API request
      const response = await axios.put(
        `${this.API_BASE_URL}/templates/${item.designId}`,
        apiData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      // Check if the request was successful
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to sync design');
      }

      console.log(`✅ Successfully synced design ${item.designId} to server`);

      // Update state
      this.updateState({
        syncStatus: 'saved',
        lastSaved: new Date(),
        isDirty: false,
        lastError: null,
      });

      // Show success notification if enabled
      if (this.options.showNotifications) {
        toast.success('Design saved successfully');
      }
    } catch (error) {
      console.error('❌ Failed to sync item to server:', error);

      // Increment retry count
      item.retryCount++;

      // If we've exceeded the max retries, remove the item from the queue
      if (item.retryCount > this.options.maxRetries) {
        console.warn(`⚠️ Exceeded max retries for item ${item.id}, removing from queue`);
        this.syncQueue.shift();

        // Update state
        this.updateState({
          syncStatus: 'error',
          lastError: error instanceof Error ? error.message : 'Unknown error',
        });

        // Show error notification if enabled
        if (this.options.showNotifications) {
          toast.error('Failed to save design', {
            description: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      } else {
        // Otherwise, move the item to the end of the queue for retry
        this.syncQueue.shift();
        this.syncQueue.push(item);
      }

      throw error;
    }
  }

  /**
   * Cleanup method to remove listeners and timers
   */
  cleanup(): void {
    // Clear all timers
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    if (this.previewDebounceTimer) {
      clearTimeout(this.previewDebounceTimer);
      this.previewDebounceTimer = null;
    }

    // Remove online status listener
    if (this.onlineStatusListener) {
      window.removeEventListener('online', this.onlineStatusListener);
      window.removeEventListener('offline', this.onlineStatusListener);
      this.onlineStatusListener = null;
    }

    // Clear listeners
    this.listeners = [];

    console.log('🧹 Enhanced sync service cleaned up');
  }
}

// Export singleton instance
export const enhancedSyncService = new EnhancedSyncService();
