/**
 * Enhanced Sync Service for Canva Editor
 * Provides unified synchronization with conflict resolution and proper ID management
 */

import { designService } from './designService';
import { generateObjectId, requestDeduplicator } from '../utils/idGenerator';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import { domToPng } from 'modern-screenshot';

export interface SyncState {
  designId: string | null;
  designName: string;
  isDirty: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
  lastError: string | null;
  syncStatus: 'idle' | 'saving' | 'saved' | 'error' | 'offline';
  isOnline: boolean;
  retryCount: number;
  conflictResolution: 'none' | 'local' | 'remote';
}

export interface SyncOptions {
  autoSaveInterval?: number;
  debounceDelay?: number;
  maxRetries?: number;
  enableLocalBackup?: boolean;
  showNotifications?: boolean;
  enableOptimisticUpdates?: boolean;
  enableConflictResolution?: boolean;
  previewImageQuality?: number;
  previewImageSize?: { width: number; height: number };
}

export interface DesignData {
  [key: string]: any;
}

export interface PreviewImageOptions {
  element: HTMLElement;
  width?: number;
  height?: number;
  quality?: number;
  designId?: string; // Add design ID for validation
}

export interface SyncQueueItem {
  id: string;
  designId: string;
  data: DesignData;
  designName: string;
  previewImage?: string;
  timestamp: number;
  retryCount: number;
}

/**
 * Enhanced Sync Service Class
 */
export class EnhancedSyncService {
  private state: SyncState = {
    designId: null,
    designName: 'Untitled Design',
    isDirty: false,
    isSaving: false,
    lastSaved: null,
    lastError: null,
    syncStatus: 'idle',
    isOnline: navigator.onLine,
    retryCount: 0,
    conflictResolution: 'none',
  };

  private options: Required<SyncOptions> = {
    autoSaveInterval: 30000, // 30 seconds
    debounceDelay: 3000, // 3 seconds
    maxRetries: 3,
    enableLocalBackup: true,
    showNotifications: true,
    enableOptimisticUpdates: true,
    enableConflictResolution: true,
    previewImageQuality: 0.8,
    previewImageSize: { width: 300, height: 300 },
  };

  private listeners: Array<(state: SyncState) => void> = [];
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;
  private previewDebounceTimer: NodeJS.Timeout | null = null;
  private lastDataSnapshot: string = '';
  private lastPreviewSnapshot: string = '';
  private userId: string | null = null;
  private syncQueue: SyncQueueItem[] = [];
  private isProcessingQueue: boolean = false;
  private onlineStatusListener: (() => void) | null = null;
  private API_BASE_URL: string = 'http://localhost:4000/api';

  // Preview generation locks per design ID to prevent cross-design updates
  private previewGenerationLocks: Map<string, Promise<string>> = new Map();
  private activePreviewGenerations: Set<string> = new Set();

  constructor(options: Partial<SyncOptions> = {}) {
    this.options = { ...this.options, ...options };
    this.setupOnlineStatusListener();
  }

  /**
   * Initialize the sync service with user context
   * Note: This does NOT load design data - that should be done separately via loadDesign()
   */
  initialize(userId: string, designId?: string, designName?: string): void {
    console.log('🔄 Initializing Enhanced Sync Service', { userId, designId, designName });

    // If we're switching to a different design, clean up first
    if (this.state.designId && designId && this.state.designId !== designId) {
      console.log(`🔄 Switching from design ${this.state.designId} to ${designId}, cleaning up...`);
      this.cleanup();
    }

    this.userId = userId;
    this.updateState({
      designId: designId || null,
      designName: designName || 'Untitled Design',
      isDirty: false,
      isSaving: false,
      lastSaved: null,
      lastError: null,
      syncStatus: 'idle',
    });

    // Only start auto-save if we have a design ID
    if (designId) {
      this.startAutoSave();
    }

    console.log('✅ Enhanced Sync Service initialized (data loading handled separately)');
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: SyncState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current state
   */
  getState(): SyncState {
    return { ...this.state };
  }

  /**
   * Validate that an operation is for the current design context
   */
  private validateDesignContext(operationDesignId: string, operationName: string): boolean {
    if (!this.state.designId) {
      console.warn(`⚠️ ${operationName}: No current design context set`);
      return false;
    }

    if (operationDesignId !== this.state.designId) {
      console.warn(`⚠️ ${operationName}: Design ID mismatch. Current: ${this.state.designId}, Operation: ${operationDesignId}`);
      return false;
    }

    return true;
  }

  /**
   * Validate that an operation is for the current user context
   */
  private validateUserContext(operationUserId: string, operationName: string): boolean {
    if (!this.userId) {
      console.warn(`⚠️ ${operationName}: No current user context set`);
      return false;
    }

    if (operationUserId !== this.userId) {
      console.warn(`⚠️ ${operationName}: User ID mismatch. Current: ${this.userId}, Operation: ${operationUserId}`);
      return false;
    }

    return true;
  }

  /**
   * Clean up resources when switching designs or destroying the service
   */
  cleanup(): void {
    console.log('🧹 Cleaning up Enhanced Sync Service resources');

    // Clear all timers
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    if (this.previewDebounceTimer) {
      clearTimeout(this.previewDebounceTimer);
      this.previewDebounceTimer = null;
    }

    // Clear preview generation locks and active generations
    this.previewGenerationLocks.clear();
    this.activePreviewGenerations.clear();

    // Clear sync queue for the current design
    if (this.state.designId) {
      this.syncQueue = this.syncQueue.filter(item => item.designId !== this.state.designId);

      // Clean up request deduplication for this design
      requestDeduplicator.cleanupDesign(this.state.designId);
    }

    // Clean up request deduplication for this user
    if (this.userId) {
      requestDeduplicator.cleanupUser(this.userId);
    }

    // Reset snapshots
    this.lastDataSnapshot = '';
    this.lastPreviewSnapshot = '';

    console.log('✅ Enhanced Sync Service cleanup completed');
  }

  /**
   * Switch to a different design context with proper cleanup
   */
  switchDesign(newDesignId: string, newDesignName?: string): void {
    const oldDesignId = this.state.designId;

    if (oldDesignId === newDesignId) {
      console.log(`ℹ️ Already in context of design ${newDesignId}, no switch needed`);
      return;
    }

    console.log(`🔄 Switching design context from ${oldDesignId} to ${newDesignId}`);

    // Clean up old design context
    if (oldDesignId) {
      // Clean up sync queue for old design
      this.syncQueue = this.syncQueue.filter(item => item.designId !== oldDesignId);

      // Clean up request deduplication for old design
      requestDeduplicator.cleanupDesign(oldDesignId);

      // Clear preview generation locks for old design
      const oldDesignKeys = Array.from(this.previewGenerationLocks.keys())
        .filter(key => key.includes(oldDesignId));
      oldDesignKeys.forEach(key => {
        this.previewGenerationLocks.delete(key);
        this.activePreviewGenerations.delete(oldDesignId);
      });
    }

    // Update state for new design
    this.updateState({
      designId: newDesignId,
      designName: newDesignName || 'Untitled Design',
      isDirty: false,
      isSaving: false,
      lastSaved: null,
      lastError: null,
      syncStatus: 'idle',
    });

    // Reset snapshots for new design
    this.lastDataSnapshot = '';
    this.lastPreviewSnapshot = '';

    // Start auto-save for new design
    if (newDesignId) {
      this.startAutoSave();
    }

    console.log(`✅ Successfully switched to design ${newDesignId}`);
  }

  /**
   * Update design data and trigger sync
   */
  updateDesignData(
    data: DesignData,
    designName?: string,
    pageElement?: HTMLElement
  ): void {
    const dataSnapshot = JSON.stringify(data);

    // Check if data actually changed
    if (dataSnapshot === this.lastDataSnapshot) {
      return;
    }

    this.lastDataSnapshot = dataSnapshot;

    // Update state
    this.updateState({
      isDirty: true,
      designName: designName || this.state.designName,
    });

    // Save to local backup if enabled
    if (this.options.enableLocalBackup) {
      this.saveToLocalBackup(data, this.state.designName);
    }

    // Generate preview image if element is provided
    let previewPromise: Promise<string> = Promise.resolve('');
    if (pageElement && this.state.designId) {
      console.log('📸 Page element provided for preview generation:', {
        designId: this.state.designId,
        tagName: pageElement.tagName,
        className: pageElement.className,
        width: pageElement.clientWidth,
        height: pageElement.clientHeight
      });
      previewPromise = this.debouncedGeneratePreview({
        element: pageElement,
        designId: this.state.designId,
      });
    } else {
      console.log('⚠️ No page element or design ID provided for preview generation');
    }

    // Trigger debounced save with preview
    previewPromise.then(previewImage => {
      this.debouncedSave(data, previewImage);
    });
  }

  /**
   * Force immediate save
   */
  async saveNow(
    data: DesignData,
    pageElement?: HTMLElement
  ): Promise<boolean> {
    // Generate preview image if element is provided
    let previewImage = '';
    if (pageElement && this.state.designId) {
      try {
        previewImage = await this.generatePreviewImage({
          element: pageElement,
          designId: this.state.designId,
        });
      } catch (error) {
        console.warn('⚠️ Failed to generate preview image for manual save:', error);
      }
    }

    return this.performSave(data, true, previewImage);
  }

  /**
   * Load existing design data from server
   */
  async loadDesign(designId: string): Promise<{ success: boolean; data?: DesignData; designName?: string; error?: string }> {
    try {
      console.log(`📥 Loading design data for ID: ${designId}`);

      this.updateState({
        syncStatus: 'saving', // Use saving status to show loading
        lastError: null,
      });

      // Validate design ID format
      const isValidObjectId = (id: string) => /^[0-9a-fA-F]{24}$/.test(id);
      if (!isValidObjectId(designId)) {
        throw new Error(`Invalid design ID format: ${designId}`);
      }

      // Fetch design metadata
      const response = await axios.get(`${this.API_BASE_URL}/templates/${designId}`, {
        timeout: 30000,
        headers: { 'Accept': 'application/json' },
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to fetch design');
      }

      const design = response.data.template || response.data;

      if (!design) {
        throw new Error('Design not found');
      }

      console.log(`✅ Design metadata loaded: ${design.title}`);

      // Update sync service state with loaded design
      this.updateState({
        designId,
        designName: design.title || 'Untitled Design',
        syncStatus: 'saved',
        lastSaved: design.updatedAt ? new Date(design.updatedAt) : null,
        isDirty: false,
        lastError: null,
      });

      // Load design data if available
      if (design.templateUrl) {
        try {
          console.log(`📥 Loading design data from URL: ${design.templateUrl}`);

          // Use the proxy-template endpoint with proper URL encoding
          const proxyUrl = `${this.API_BASE_URL}/proxy-template/${encodeURIComponent(design.templateUrl)}`;
          console.log(`📥 Proxy URL: ${proxyUrl}`);

          const templateResponse = await axios.get(proxyUrl, {
            timeout: 30000,
            headers: { 'Accept': 'application/json' },
          });

          if (templateResponse.data) {
            console.log(`✅ Design data loaded successfully`);
            return {
              success: true,
              data: templateResponse.data,
              designName: design.title || 'Untitled Design',
            };
          } else {
            throw new Error('Empty design data received');
          }
        } catch (templateError) {
          console.warn('⚠️ Failed to load design data from URL:', templateError);
          throw new Error('Failed to load design content');
        }
      } else if (design.packedData && design.packedData.length > 0) {
        // Use packed data if available
        console.log(`✅ Using packed data from design`);
        return {
          success: true,
          data: design.packedData[0], // Use first packed data item
          designName: design.title || 'Untitled Design',
        };
      } else {
        // No design data available - return empty template
        console.log(`⚠️ No design data found, returning empty template`);
        return {
          success: true,
          data: { pages: [] }, // Empty template
          designName: design.title || 'Untitled Design',
        };
      }

    } catch (error) {
      console.error('❌ Failed to load design:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      this.updateState({
        syncStatus: 'error',
        lastError: errorMessage,
        isSaving: false,
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Create a new design with pre-generated ID
   */
  async createNewDesign(
    data: DesignData,
    designName: string,
    options: {
      width?: number;
      height?: number;
      backgroundColor?: string;
      isCoupon?: boolean;
    } = {}
  ): Promise<string | null> {
    if (!this.userId) {
      throw new Error('User ID not set. Call initialize() first.');
    }

    try {
      this.updateState({ isSaving: true, syncStatus: 'saving' });

      const response = await designService.createDesignWithRetry({
        templateName: designName,
        templateDesc: `New design created on ${new Date().toLocaleDateString()}`,
        userId: this.userId,
        packedData: [data], // Wrap in array as expected by API
        previewImage: '', // Will be generated later
        tags: options.isCoupon ? ['coupon'] : [],
        isPublic: false,
        isKiosk: false,
        isCoupon: options.isCoupon || false,
        width: options.width,
        height: options.height,
        backgroundColor: options.backgroundColor,
      });

      const designId = response.template.id;
      
      this.updateState({
        designId,
        designName,
        isDirty: false,
        isSaving: false,
        lastSaved: new Date(),
        lastError: null,
        syncStatus: 'saved',
      });

      console.log(`✅ New design created with ID: ${designId}`);
      return designId;

    } catch (error: any) {
      console.error('❌ Failed to create new design:', error);
      
      this.updateState({
        isSaving: false,
        lastError: error.message,
        syncStatus: 'error',
      });

      throw error;
    }
  }

  /**
   * Update design name
   */
  updateDesignName(newName: string): void {
    if (newName !== this.state.designName) {
      this.updateState({
        designName: newName,
        isDirty: true,
      });
    }
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.listeners = [];
  }

  // Private methods

  private updateState(updates: Partial<SyncState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));

    // Dispatch design-specific sync status event
    if (this.state.designId) {
      const eventName = `sync-status-changed-${this.state.designId}`;
      window.dispatchEvent(new CustomEvent(eventName, {
        detail: {
          designId: this.state.designId,
          syncState: this.state
        }
      }));
    }
  }

  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    this.autoSaveTimer = setInterval(() => {
      if (this.state.isDirty && !this.state.isSaving) {
        console.log('⏰ Auto-save triggered by timer');
        // We need the current data, but we don't have it here
        // This will be handled by the component using this service
      }
    }, this.options.autoSaveInterval);
  }

  private debouncedSave(data: DesignData, previewImage?: string): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      if (!this.state.isSaving) {
        this.performSave(data, false, previewImage);
      }
    }, this.options.debounceDelay);
  }

  private async performSave(
    data: DesignData,
    isManual: boolean,
    previewImage?: string
  ): Promise<boolean> {
    // Enhanced validation for save operations
    if (!this.userId) {
      console.error('❌ Cannot save: User ID not set');
      return false;
    }

    if (!this.state.designId) {
      console.error('❌ Cannot save: Design ID not set');
      return false;
    }

    if (this.state.isSaving) {
      console.log('⏸️ Save already in progress, skipping');
      return false;
    }

    // Validate design context for save operation
    if (!this.validateDesignContext(this.state.designId, 'performSave')) {
      return false;
    }

    try {
      this.updateState({ isSaving: true, syncStatus: 'saving' });

      const saveKey = `save-${this.state.designId || 'new'}-${Date.now()}`;

      // Create a queue item with preview image if available
      const queueItem: SyncQueueItem = {
        id: generateObjectId(),
        designId: this.state.designId || 'new',
        data,
        designName: this.state.designName,
        previewImage,
        timestamp: Date.now(),
        retryCount: 0,
      };

      // Use enhanced request deduplication for non-manual saves
      const saveOperation = isManual
        ? this.syncItemToServer(queueItem)
        : requestDeduplicator.deduplicate(
            saveKey,
            () => this.syncItemToServer(queueItem),
            {
              userId: this.userId || undefined,
              designId: this.state.designId || undefined,
              timeoutMs: 30000,
            }
          );

      await saveOperation;

      this.updateState({
        isDirty: false,
        isSaving: false,
        lastSaved: new Date(),
        lastError: null,
        syncStatus: 'saved',
      });

     

      return true;

    } catch (error: any) {
      console.error('❌ Save failed:', error);
      
      this.updateState({
        isSaving: false,
        lastError: error.message,
        syncStatus: 'error',
      });

      if (this.options.showNotifications) {
        toast.error('Failed to save design', {
          description: error.message,
          duration: 5000,
        });
      }

      return false;
    }
  }

  private async executeSave(data: DesignData): Promise<void> {
    // Implementation depends on whether this is a new design or existing one
    if (!this.state.designId) {
      // This is a new design - should have been created via createNewDesign
      throw new Error('Cannot save design without ID. Create design first.');
    }

    console.log(`💾 Saving design ${this.state.designId}...`);

    // If we're offline, add to sync queue
    if (!navigator.onLine) {
      this.addToSyncQueue({
        designId: this.state.designId,
        data,
        designName: this.state.designName,
      });

      this.updateState({
        syncStatus: 'offline',
        lastError: 'Device is offline. Changes will be synced when connection is restored.',
      });

      return;
    }

    // Try to sync directly to server
    try {
      await this.syncItemToServer({
        id: generateObjectId(),
        designId: this.state.designId,
        data,
        designName: this.state.designName,
        timestamp: Date.now(),
        retryCount: 0,
      });
    } catch (error) {
      // If direct sync fails, add to queue for retry
      this.addToSyncQueue({
        designId: this.state.designId,
        data,
        designName: this.state.designName,
      });

      throw error;
    }
  }

  private saveToLocalBackup(data: DesignData, designName: string): void {
    try {
      const backup = {
        designId: this.state.designId,
        designName,
        data,
        timestamp: Date.now(),
      };

      localStorage.setItem('design_backup', JSON.stringify(backup));
      console.log('💾 Design backed up to localStorage');
    } catch (error) {
      console.warn('⚠️ Failed to save local backup:', error);
    }
  }

  /**
   * Generate a high-quality preview image from the design with design ID validation
   */
  async generatePreviewImage(options: PreviewImageOptions): Promise<string> {
    if (!options.element) {
      console.warn('⚠️ No element provided for preview generation');
      return '';
    }

    // Validate design ID context - ensure we're generating preview for the correct design
    const targetDesignId = options.designId || this.state.designId;
    if (!targetDesignId) {
      console.warn('⚠️ No design ID provided for preview generation - skipping for safety');
      return '';
    }

    // Validate that this matches the current design context
    if (this.state.designId && targetDesignId !== this.state.designId) {
      console.warn(`⚠️ Design ID mismatch in preview generation. Expected: ${this.state.designId}, Got: ${targetDesignId}`);
      return '';
    }

    // Check if preview generation is already in progress for this design
    if (this.activePreviewGenerations.has(targetDesignId)) {
      console.log(`🔄 Preview generation already in progress for design ${targetDesignId}, waiting...`);
      const existingPromise = this.previewGenerationLocks.get(targetDesignId);
      if (existingPromise) {
        return existingPromise;
      }
    }

    // Mark this design as having active preview generation
    this.activePreviewGenerations.add(targetDesignId);

    try {
      console.log('📸 Starting preview image generation...', {
        designId: targetDesignId,
        elementTag: options.element.tagName,
        elementClass: options.element.className,
        elementWidth: options.element.clientWidth,
        elementHeight: options.element.clientHeight,
      });

      // Calculate dimensions while maintaining aspect ratio
      const elementWidth = options.element.clientWidth;
      const elementHeight = options.element.clientHeight;

      if (elementWidth === 0 || elementHeight === 0) {
        console.warn('⚠️ Element has zero dimensions, cannot generate preview');
        return '';
      }

      const aspectRatio = elementWidth / elementHeight;

      // Default to 300x300 or use provided dimensions
      const maxWidth = options.width || this.options.previewImageSize.width;
      const maxHeight = options.height || this.options.previewImageSize.height;

      let targetWidth, targetHeight;

      if (aspectRatio > 1) {
        // Landscape orientation
        targetWidth = Math.min(maxWidth, elementWidth);
        targetHeight = targetWidth / aspectRatio;
      } else {
        // Portrait or square orientation
        targetHeight = Math.min(maxHeight, elementHeight);
        targetWidth = targetHeight * aspectRatio;
      }

      console.log(`🖼️ Generating preview image for design ${targetDesignId} at ${Math.round(targetWidth)}x${Math.round(targetHeight)}`);

      // Generate the image using domToPng
      const previewImage = await domToPng(options.element, {
        width: Math.round(targetWidth),
        height: Math.round(targetHeight),
        quality: options.quality || this.options.previewImageQuality,
      });

      console.log(`✅ Preview image generated successfully for design ${targetDesignId}, size: ${previewImage.length} bytes`);
      return previewImage;
    } catch (error) {
      console.error(`❌ Failed to generate preview image for design ${targetDesignId}:`, error);
      return '';
    } finally {
      // Clean up the active generation tracking
      this.activePreviewGenerations.delete(targetDesignId);
      this.previewGenerationLocks.delete(targetDesignId);
    }
  }

  /**
   * Debounced preview image generation with design ID isolation
   */
  debouncedGeneratePreview(options: PreviewImageOptions): Promise<string> {
    const targetDesignId = options.designId || this.state.designId;

    if (!targetDesignId) {
      console.warn('⚠️ No design ID for debounced preview generation');
      return Promise.resolve('');
    }

    // Check if there's already a pending generation for this design
    const existingPromise = this.previewGenerationLocks.get(targetDesignId);
    if (existingPromise) {
      console.log(`🔄 Reusing existing preview generation promise for design ${targetDesignId}`);
      return existingPromise;
    }

    // Create a new promise for this design's preview generation
    const previewPromise = new Promise<string>((resolve) => {
      // Clear any existing debounce timer
      if (this.previewDebounceTimer) {
        clearTimeout(this.previewDebounceTimer);
      }

      // Set a new debounce timer
      this.previewDebounceTimer = setTimeout(async () => {
        try {
          const previewImage = await this.generatePreviewImage({
            ...options,
            designId: targetDesignId,
          });
          resolve(previewImage);
        } catch (error) {
          console.error(`❌ Error in debounced preview generation for design ${targetDesignId}:`, error);
          resolve('');
        }
      }, 500); // 500ms debounce delay for preview generation
    });

    // Store the promise to prevent duplicate generations
    this.previewGenerationLocks.set(targetDesignId, previewPromise);

    return previewPromise;
  }

  /**
   * Set up online status listener
   */
  private setupOnlineStatusListener(): void {
    // Clean up any existing listener
    if (this.onlineStatusListener) {
      window.removeEventListener('online', this.onlineStatusListener);
      window.removeEventListener('offline', this.onlineStatusListener);
    }

    // Create a new listener
    this.onlineStatusListener = () => {
      const isOnline = navigator.onLine;

      // Update state with online status
      this.updateState({
        isOnline,
        syncStatus: isOnline ? this.state.syncStatus : 'offline',
      });

      // If we're back online, process the sync queue
      if (isOnline && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    };

    // Add the listener to online/offline events
    window.addEventListener('online', this.onlineStatusListener);
    window.addEventListener('offline', this.onlineStatusListener);

    // Set initial state
    this.updateState({ isOnline: navigator.onLine });
  }

  /**
   * Add an item to the sync queue
   */
  private addToSyncQueue(item: Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>): void {
    const queueItem: SyncQueueItem = {
      ...item,
      id: generateObjectId(),
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.syncQueue.push(queueItem);
    console.log(`📋 Added item to sync queue, queue length: ${this.syncQueue.length}`);

    // If we're online, process the queue
    if (navigator.onLine && !this.isProcessingQueue) {
      this.processSyncQueue();
    }
  }

  /**
   * Process the sync queue with design context validation
   */
  private async processSyncQueue(): Promise<void> {
    // If we're offline or already processing, return
    if (!navigator.onLine || this.isProcessingQueue || this.syncQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`🔄 Processing sync queue, ${this.syncQueue.length} items remaining`);

    try {
      // Filter queue to only include items for the current design context
      const currentDesignItems = this.syncQueue.filter(item =>
        this.state.designId && item.designId === this.state.designId
      );

      if (currentDesignItems.length === 0) {
        console.log('ℹ️ No items in sync queue for current design context');
        return;
      }

      // Get the next item for the current design
      const item = currentDesignItems[0];
      const itemIndex = this.syncQueue.indexOf(item);

      // Validate design context before processing
      if (!this.validateDesignContext(item.designId, 'processSyncQueue')) {
        console.warn(`⚠️ Removing invalid sync queue item for design ${item.designId}`);
        this.syncQueue.splice(itemIndex, 1);

        // Continue processing if there are more items
        if (this.syncQueue.length > 0) {
          this.processSyncQueue();
        }
        return;
      }

      // Try to sync the item
      await this.syncItemToServer(item);

      // If successful, remove the item from the queue
      this.syncQueue.splice(itemIndex, 1);

      // If there are more items in the queue, process the next one
      if (this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    } catch (error) {
      console.error('❌ Failed to process sync queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Sync a queue item to the server with design ID validation
   */
  private async syncItemToServer(item: SyncQueueItem): Promise<void> {
    try {
      // CRITICAL: Validate design ID to prevent cross-design updates
      if (!item.designId || item.designId === 'new') {
        throw new Error('Cannot sync item without valid design ID');
      }

      // Validate that this sync operation is for the current design context
      if (this.state.designId && item.designId !== this.state.designId) {
        console.warn(`⚠️ Design ID mismatch in sync operation. Current: ${this.state.designId}, Sync item: ${item.designId}`);
        throw new Error(`Design ID mismatch: cannot sync design ${item.designId} in context of ${this.state.designId}`);
      }

      // Validate user context
      if (!this.userId) {
        throw new Error('Cannot sync without user context');
      }

      // Prepare the API request - DO NOT override userId in body
      const apiData = {
        packedData: [item.data],
        previewImage: item.previewImage || '',
        templateName: item.designName,
        templateDesc: '',
        tags: [],
        // DO NOT include userId in body - let backend preserve original owner
      };

      console.log('📤 Syncing to server with validation:', {
        designId: item.designId,
        currentDesignId: this.state.designId,
        userId: this.userId,
        designName: item.designName,
        hasPreviewImage: !!(item.previewImage && item.previewImage.length > 0),
        previewImageSize: item.previewImage ? item.previewImage.length : 0,
      });

      // Make the API request with design ID validation
      const response = await axios.put(
        `${this.API_BASE_URL}/templates/${item.designId}`,
        apiData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Design-Context': this.state.designId || '', // Add design context header
            'X-User-Context': this.userId || '', // Add user context header
          },
        }
      );

      // Check if the request was successful
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to sync design');
      }

      console.log(`✅ Successfully synced design ${item.designId} to server (validated)`);

      // Only update state if this sync is for the current design
      if (item.designId === this.state.designId) {
        this.updateState({
          syncStatus: 'saved',
          lastSaved: new Date(),
          isDirty: false,
          lastError: null,
        });

  
      } else {
        console.log(`ℹ️ Sync completed for design ${item.designId}, but not updating state (different from current design ${this.state.designId})`);
      }
    } catch (error) {
      console.error('❌ Failed to sync item to server:', error);

      // Increment retry count
      item.retryCount++;

      // If we've exceeded the max retries, remove the item from the queue
      if (item.retryCount > this.options.maxRetries) {
        console.warn(`⚠️ Exceeded max retries for item ${item.id}, removing from queue`);
        this.syncQueue.shift();

        // Update state
        this.updateState({
          syncStatus: 'error',
          lastError: error instanceof Error ? error.message : 'Unknown error',
        });

        // Show error notification if enabled
        if (this.options.showNotifications) {
          toast.error('Failed to save design', {
            description: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      } else {
        // Otherwise, move the item to the end of the queue for retry
        this.syncQueue.shift();
        this.syncQueue.push(item);
      }

      throw error;
    }
  }


}

// Export singleton instance for backward compatibility (but should be avoided)
export const enhancedSyncService = new EnhancedSyncService();
