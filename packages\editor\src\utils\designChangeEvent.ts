/**
 * Utility to dispatch design change events
 * This is used to trigger auto-save when the design changes
 */

// Debounce function to limit event dispatches with leading edge control
const debounce = (func: Function, wait: number, immediate = false) => {
  let timeout: NodeJS.Timeout | null = null;
  let lastArgs: any[] | null = null;
  let lastCallTime = 0;

  return function executedFunction(...args: any[]) {
    const now = Date.now();
    const timeSinceLastCall = now - lastCallTime;

    // Store the latest arguments
    lastArgs = args;
    lastCallTime = now;

    // If we're within the cooldown period, don't schedule a new timeout
    if (timeSinceLastCall < 200 && !immediate) {
      return;
    }

    // Execute immediately if requested and no timeout is pending
    if (immediate && !timeout) {
      func(...args);
    }

    // Clear existing timeout
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    // Schedule new timeout
    timeout = setTimeout(() => {
      timeout = null;
      if (!immediate && lastArgs) {
        func(...lastArgs);
        lastArgs = null;
      }
    }, wait);
  };
};

// Track changes to avoid duplicate events per design
const lastChangeHashes = new Map<string, string>();

// Create a custom event for design changes - DESIGN-SPECIFIC
export const dispatchDesignChangeEvent = (designId?: string) => {
  const targetDesignId = designId || 'new';

  // Generate a simple hash of the current time to track changes
  const currentHash = Date.now().toString();
  const lastHash = lastChangeHashes.get(targetDesignId);

  // Only dispatch if this is a new change (at least 500ms since last change)
  if (currentHash !== lastHash) {
    lastChangeHashes.set(targetDesignId, currentHash);

    // Dispatch design-specific event
    const eventName = `design-changed-${targetDesignId}`;
    window.dispatchEvent(new CustomEvent(eventName, {
      detail: { designId: targetDesignId }
    }));
    console.log(`Design change event dispatched for design: ${targetDesignId}`);
  }
};

// Debounced version to avoid too many events (1000ms delay, no immediate execution)
// Create a function that returns a debounced version for a specific design
export const createDebouncedDispatchDesignChangeEvent = (designId?: string) => {
  return debounce(() => dispatchDesignChangeEvent(designId), 1000, false);
};

// Legacy function for backward compatibility
export const debouncedDispatchDesignChangeEvent = createDebouncedDispatchDesignChangeEvent();

// Function to set up listeners for common design change actions - DESIGN-SPECIFIC
export const setupDesignChangeListeners = (designId?: string) => {
  const debouncedDispatch = createDebouncedDispatchDesignChangeEvent(designId);

  // Listen for history changes (undo/redo)
  window.addEventListener("history-changed", debouncedDispatch);

  // Listen for layer changes
  window.addEventListener("layer-changed", debouncedDispatch);

  // Listen for text changes
  window.addEventListener("text-changed", debouncedDispatch);

  // Listen for page changes
  window.addEventListener("page-changed", debouncedDispatch);

  // Store the debounced function for cleanup
  (window as any)[`designChangeListener_${designId || 'new'}`] = debouncedDispatch;
};

// Function to clean up listeners - DESIGN-SPECIFIC
export const cleanupDesignChangeListeners = (designId?: string) => {
  const debouncedDispatch = (window as any)[`designChangeListener_${designId || 'new'}`];

  if (debouncedDispatch) {
    window.removeEventListener("history-changed", debouncedDispatch);
    window.removeEventListener("layer-changed", debouncedDispatch);
    window.removeEventListener("text-changed", debouncedDispatch);
    window.removeEventListener("page-changed", debouncedDispatch);

    // Clean up the stored reference
    delete (window as any)[`designChangeListener_${designId || 'new'}`];
  }
};

export default {
  dispatchDesignChangeEvent,
  debouncedDispatchDesignChangeEvent,
  setupDesignChangeListeners,
  cleanupDesignChangeListeners,
};
