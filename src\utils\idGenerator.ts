/**
 * MongoDB ObjectId generation utility for frontend
 * Generates valid MongoDB ObjectIds to prevent duplicate design creation
 */

/**
 * Generates a valid MongoDB ObjectId string
 * Based on MongoDB ObjectId specification: 12-byte identifier consisting of:
 * - 4-byte timestamp (seconds since Unix epoch)
 * - 5-byte random value unique to machine and process
 * - 3-byte incrementing counter
 */
export const generateObjectId = (): string => {
  // Get current timestamp (4 bytes)
  const timestamp = Math.floor(Date.now() / 1000);
  
  // Generate random 5-byte value (machine + process identifier)
  const randomBytes = new Array(5);
  for (let i = 0; i < 5; i++) {
    randomBytes[i] = Math.floor(Math.random() * 256);
  }
  
  // Generate 3-byte counter (incremented for each call)
  const counter = generateObjectId.counter++;
  if (generateObjectId.counter >= 0xFFFFFF) {
    generateObjectId.counter = 0;
  }
  
  // Convert to hex string
  const timestampHex = timestamp.toString(16).padStart(8, '0');
  const randomHex = randomBytes.map(b => b.toString(16).padStart(2, '0')).join('');
  const counterHex = counter.toString(16).padStart(6, '0');
  
  return timestampHex + randomHex + counterHex;
};

// Initialize counter with random value
generateObjectId.counter = Math.floor(Math.random() * 0xFFFFFF);

/**
 * Validates if a string is a valid MongoDB ObjectId
 */
export const isValidObjectId = (id: string): boolean => {
  return /^[0-9a-fA-F]{24}$/.test(id);
};

/**
 * Design creation request tracker to prevent duplicate requests
 */
class DesignCreationTracker {
  private pendingRequests = new Map<string, Promise<any>>();
  private createdDesigns = new Set<string>();

  /**
   * Tracks a design creation request to prevent duplicates
   * @param designId - The pre-generated design ID
   * @param requestPromise - The API request promise
   * @returns The same promise or existing promise if already in progress
   */
  trackRequest(designId: string, requestPromise: Promise<any>): Promise<any> {
    // If request is already in progress, return existing promise
    if (this.pendingRequests.has(designId)) {
      console.log(`🔄 Design creation already in progress for ID: ${designId}`);
      return this.pendingRequests.get(designId)!;
    }

    // If design was already created, reject
    if (this.createdDesigns.has(designId)) {
      console.log(`❌ Design already created with ID: ${designId}`);
      return Promise.reject(new Error(`Design with ID ${designId} already exists`));
    }

    // Track the new request
    console.log(`🆕 Tracking new design creation for ID: ${designId}`);
    this.pendingRequests.set(designId, requestPromise);

    // Clean up when request completes
    requestPromise
      .then(() => {
        this.createdDesigns.add(designId);
        this.pendingRequests.delete(designId);
        console.log(`✅ Design creation completed for ID: ${designId}`);
      })
      .catch((error) => {
        this.pendingRequests.delete(designId);
        console.log(`❌ Design creation failed for ID: ${designId}`, error);
      });

    return requestPromise;
  }

  /**
   * Checks if a design creation is in progress
   */
  isCreationInProgress(designId: string): boolean {
    return this.pendingRequests.has(designId);
  }

  /**
   * Checks if a design was already created
   */
  isDesignCreated(designId: string): boolean {
    return this.createdDesigns.has(designId);
  }

  /**
   * Clears tracking for a specific design (useful for cleanup)
   */
  clearTracking(designId: string): void {
    this.pendingRequests.delete(designId);
    this.createdDesigns.delete(designId);
  }

  /**
   * Gets all pending request IDs (for debugging)
   */
  getPendingRequests(): string[] {
    return Array.from(this.pendingRequests.keys());
  }
}

// Global instance for tracking design creation
export const designCreationTracker = new DesignCreationTracker();

/**
 * Enhanced request deduplication utility with design and user context
 * Prevents multiple identical requests from being sent simultaneously
 */
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();
  private requestTimeouts = new Map<string, NodeJS.Timeout>();

  /**
   * Deduplicates requests based on a key with enhanced context
   * @param key - Unique key for the request
   * @param requestFn - Function that returns the request promise
   * @param options - Additional options for deduplication
   * @returns Promise that resolves with the request result
   */
  async deduplicate<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: {
      userId?: string;
      designId?: string;
      timeoutMs?: number;
    } = {}
  ): Promise<T> {
    // Create enhanced key with user and design context
    const enhancedKey = this.createEnhancedKey(key, options.userId, options.designId);

    // If request is already pending, return existing promise
    if (this.pendingRequests.has(enhancedKey)) {
      console.log(`🔄 Deduplicating request for enhanced key: ${enhancedKey}`);
      return this.pendingRequests.get(enhancedKey) as Promise<T>;
    }

    console.log(`🆕 Creating new request for enhanced key: ${enhancedKey}`);

    // Create new request
    const requestPromise = requestFn();
    this.pendingRequests.set(enhancedKey, requestPromise);

    // Set timeout for cleanup (default 30 seconds)
    const timeoutMs = options.timeoutMs || 30000;
    const timeoutId = setTimeout(() => {
      console.log(`⏰ Request timeout for key: ${enhancedKey}`);
      this.cleanup(enhancedKey);
    }, timeoutMs);

    this.requestTimeouts.set(enhancedKey, timeoutId);

    // Clean up when request completes
    requestPromise
      .finally(() => {
        this.cleanup(enhancedKey);
      });

    return requestPromise;
  }

  /**
   * Create enhanced key with user and design context
   */
  private createEnhancedKey(baseKey: string, userId?: string, designId?: string): string {
    const parts = [baseKey];

    if (userId) {
      parts.push(`user:${userId}`);
    }

    if (designId) {
      parts.push(`design:${designId}`);
    }

    return parts.join('|');
  }

  /**
   * Clean up request and timeout
   */
  private cleanup(key: string): void {
    this.pendingRequests.delete(key);

    const timeoutId = this.requestTimeouts.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.requestTimeouts.delete(key);
    }
  }

  /**
   * Clean up all requests for a specific design
   */
  cleanupDesign(designId: string): void {
    console.log(`🧹 Cleaning up all requests for design: ${designId}`);

    const keysToDelete: string[] = [];

    for (const key of this.pendingRequests.keys()) {
      if (key.includes(`design:${designId}`)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cleanup(key));

    console.log(`✅ Cleaned up ${keysToDelete.length} requests for design ${designId}`);
  }

  /**
   * Clean up all requests for a specific user
   */
  cleanupUser(userId: string): void {
    console.log(`🧹 Cleaning up all requests for user: ${userId}`);

    const keysToDelete: string[] = [];

    for (const key of this.pendingRequests.keys()) {
      if (key.includes(`user:${userId}`)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cleanup(key));

    console.log(`✅ Cleaned up ${keysToDelete.length} requests for user ${userId}`);
  }
}

// Global instance for request deduplication
export const requestDeduplicator = new RequestDeduplicator();

/**
 * Generates a unique request ID for idempotency
 */
export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
